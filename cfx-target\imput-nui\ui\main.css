@import url("https://fonts.googleapis.com/css2?family=Open+Sans&display=swap");

html,
body {
	width: 100%;
	height: 100%;
	font-family: "Open Sans", sans-serif;
	font-size: 0.9vw;
	user-select: none;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: row;
	overflow: hidden;
}

.main {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	margin: auto;
	color: white;
	background-color: rgba(0, 0, 0, 0.5);
	border-radius: 6px;
	box-shadow: 2px 2px 8px rgba(black, 0.3);
}

.text-box {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}

.text-inputs {
	width: 100%;
	height: 93%;
	font-family: 'Roboto', sans-serif;
	resize: none;
	display: block;
	box-sizing: content-box;
	overflow: hidden;
	background-color: #0c0d0e;
	color: white;
	border-radius: 6px;
	box-shadow: 2px 2px 8px rgba(black, 0.3);
	border: 0;
	padding: 5px;
	user-select: none;
	font-size: 0.85vw;
	outline: none;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

input[type=number] {
	-moz-appearance: textfield;
}