Config = {}

Config.Framework = "esx" -- esx = trigger / newEsx = export for legacy new version
Config.SharedObject = "esx:getSharedObject"

Config.Showrooms = {

    ["Cars"] = { -- Bateaux = catégorie de ton showroom
        --------------------
        ColorMenuR = 255, -- Bannière couleur R
        ColorMenuG = 255, -- Bannière couleur G
        ColorMenuB = 255, -- Bannière couleur B
        ColorMenuA = 150, -- Bannière couleur A
        MenuPositionX = 0, -- Bannière position X sur l'écran
        MenuPositionY = 0, -- Bannière position Y sur l'écran
        --------------------
        job = "cardealer", -- job qui a accès au menu 
        --------------------
        Marker = {
            Type = 6, -- type du marker
            Color = {R = 255, G = 255, B = 255, A = 255}, -- color du marker
            Size =  {x = 1.0, y = 1.0, z = 1.0}, -- taille du marker
            DrawDistance = 10, -- Distance ou le marker est affiché
            DrawInteract = 1.5, -- Distance ou on peux interact avec le marker
        },
        --------------------
        rotateVehicle = false,  -- true = le véhicule tournera sur lui même / false = non
        --------------------
        Menu = vector3(-573.40, -603.30, 34.72), -- menu du showroom
        --------------------
        ExpositionPosition = {
            -----------------------------------------------------------------------
            -- position = ou le véhicule seras positioné / vehicles = stockage des véhicules en expo, ne pas toucher.
            -----------------------------------------------------------------------
            {position = vector4(-588.58, -615.28, 34.72, 3.66), vehicles = {}}, 
            {position = vector4(-580.44, -615.26, 34.72, 2.67), vehicles = {}},
            {position = vector4(-569.12, -613.77, 34.83, 3.50), vehicles = {}},
            {position = vector4(-559.63, -613.66, 34.83, 6.91), vehicles = {}},
            {position = vector4(-549.51, -613.59, 34.83, 8.69), vehicles = {}},
            {position = vector4(-549.64, -586.20, 34.83, 177.08), vehicles = {}},
            {position = vector4(-559.12, -586.06, 34.83, 184.06), vehicles = {}},
            {position = vector4(-569.15, -585.97, 34.83, 184.43), vehicles = {}},
            {position = vector4(-580.03, -583.67, 34.72, 183.97), vehicles = {}},
            {position = vector4(-567.83, -599.93, 34.72, 20.80), vehicles = {}},
            {position = vector4(-545.82, -599.33, 34.72, 138.63), vehicles = {}},
            {position = vector4(-537.60, -599.06, 34.72, 140.71), vehicles = {}},
            {position = vector4(-521.69, -612.07, 34.90, 167.41), vehicles = {}},
            {position = vector4(-521.63, -599.06, 35.50, 170.88), vehicles = {}},
            {position = vector4(-521.56, -586.26, 35.50, 170.08), vehicles = {}},
        },
    },

    --- Ajoutez vos showrooms ici en copiant celui bateau en modifiant ["Bateaux"] ligne 8, job, coordonnées menu/expo. Enjoy ! 

}
